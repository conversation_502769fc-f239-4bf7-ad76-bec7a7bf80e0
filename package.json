{"name": "fuse", "version": "8.1.2", "license": "https://themeforest.net/licenses/terms/regular", "scripts": {"ng": "ng", "start": "ng serve --port 4500 --open", "start-hmr": "ng serve --configuration hmr --source-map=false --hmr-warning=false", "start-hmr-sourcemaps": "ng serve --configuration hmr --source-map=true --hmr-warning=false", "build": "node --max_old_space_size=6144 ./node_modules/@angular/cli/bin/ng build --dev", "build-stats": "node --max_old_space_size=6144 ./node_modules/@angular/cli/bin/ng build --dev --stats-json", "build-prod": "node --max_old_space_size=6144 ./node_modules/@angular/cli/bin/ng build --prod", "build-prod-stats": "node --max_old_space_size=6144 ./node_modules/@angular/cli/bin/ng build --prod --stats-json", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "bundle-report": "webpack-bundle-analyzer dist/stats.json"}, "private": true, "dependencies": {"@agm/core": "1.0.0-beta.7", "@angular/animations": "8.2.14", "@angular/cdk": "8.1.1", "@angular/common": "8.2.14", "@angular/compiler": "8.2.14", "@angular/core": "8.2.14", "@angular/flex-layout": "8.0.0-beta.26", "@angular/forms": "8.2.14", "@angular/material": "8.1.1", "@angular/material-moment-adapter": "8.1.1", "@angular/platform-browser": "8.2.14", "@angular/platform-browser-dynamic": "8.2.14", "@angular/router": "8.2.14", "@azure/msal-angular": "0.1.4", "@fortawesome/angular-fontawesome": "0.5.0", "@fortawesome/fontawesome-svg-core": "1.2.25", "@fortawesome/free-solid-svg-icons": "5.11.2", "@ngrx/effects": "8.1.0", "@ngrx/router-store": "8.1.0", "@ngrx/store": "8.1.0", "@ngrx/store-devtools": "8.1.0", "@ngx-translate/core": "11.0.1", "@swimlane/dragula": "3.8.0", "@swimlane/ngx-charts": "12.0.1", "@swimlane/ngx-datatable": "15.0.2", "@swimlane/ngx-dnd": "8.0.0", "@techiediaries/ngx-qrcode": "9.1.0", "@types/prismjs": "1.16.0", "angular-calendar": "0.27.13", "angular-font-awesome": "3.1.2", "angular-in-memory-web-api": "0.8.0", "angular-vertical-tabs": "github:SamuelMarks/angular-vertical-material-tabs", "angularx-qrcode": "2.1.4", "angularx-social-login": "2.2.1", "chart.js": "2.8.0", "classlist.js": "1.1.20150312", "d3": "5.9.7", "date-fns": "1.30.1", "font-awesome": "4.7.0", "google-libphonenumber": "3.2.1", "hammerjs": "2.0.8", "intl-tel-input": "17.0.21", "lodash": "4.17.15", "moment": "2.24.0", "msal": "1.1.3", "ng2-charts": "2.3.0", "ngrx-store-freeze": "0.2.4", "ngx-bootstrap": "5.1.0", "ngx-color-picker": "8.1.0", "ngx-cookie-service": "2.2.0", "ngx-intl-tel-input": "2.3.4", "ngx-mat-select-search": "2.2.0", "perfect-scrollbar": "1.4.0", "prismjs": "1.16.0", "rxjs": "6.5.3", "rxjs-compat": "6.5.3", "web-animations-js": "2.3.2", "xlsx": "0.18.5", "zone.js": "0.9.1"}, "devDependencies": {"@angular-devkit/build-angular": "0.803.23", "@angular/cli": "8.3.19", "@angular/compiler-cli": "8.2.14", "@angular/language-service": "8.2.14", "@angularclass/hmr": "2.1.3", "@fortawesome/fontawesome-free": "5.11.2", "@types/jasmine": "3.3.14", "@types/jasminewd2": "2.0.6", "@types/lodash": "4.14.136", "@types/node": "8.9.5", "codelyzer": "5.1.0", "jasmine-core": "3.4.0", "jasmine-spec-reporter": "4.2.1", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-coverage-istanbul-reporter": "2.0.5", "karma-jasmine": "2.0.1", "karma-jasmine-html-reporter": "1.4.2", "protractor": "5.4.2", "ts-node": "7.0.1", "tslib": "1.10.0", "tslint": "5.15.0", "typescript": "3.5.3", "webpack-bundle-analyzer": "3.3.2", "webpack-dev-middleware": "3.7.2", "webpack-hot-middleware": "2.25.0"}}