import { Component, ViewChild, On<PERSON><PERSON>roy } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { BaseComponent } from 'app/main/common/base.component';
import { environment } from 'environments/environment';
import { ModuleList } from 'app/dto/countryDto';
import { ModuleApiService } from 'app/services/api/modules.api.service';
import { userIDDto } from 'app/dto/userDto';
import { GlobalSearchService, SearchableComponent, SearchConfiguration } from 'app/services/global-search.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';


@Component({
  selector: 'app-manage-dashboard',
  templateUrl: './managedashboard.component.html',
  styleUrls: ['./managedashboard.component.scss']
})

export class ManageDashoardComponent extends BaseComponent implements On<PERSON><PERSON>roy, SearchableComponent {
  @ViewChild('formUser', { static: true }) ngForm;
  hpbsUrl = environment.hpbsUrl;
  tkn = localStorage.getItem('SSOTOKEN');
  moduleList: ModuleList[];
  roleId: string;
  userName: string;
  picUrl: string;
  showImage: boolean = false;

  // Card back side properties - track per module
  moduleCardData: { [moduleId: string]: { selectedApp: any, selectedLink: string } } = {};

  // Search functionality
  private _unsubscribeAll: Subject<any> = new Subject<any>();
  private originalModuleList: ModuleList[] = [];
  private searchConfig: SearchConfiguration = {
    searchableFields: ['moduleDesc', 'Description'],
    caseSensitive: false,
    exactMatch: false,
    minSearchLength: 0
  };

  constructor(_snackBar: MatSnackBar, public _dialog: MatDialog, private _router: Router, private _moduleApi: ModuleApiService, private _globalSearchService: GlobalSearchService) {
    super(_snackBar);
  }

  ngOnInit() {
    const isAd = localStorage.getItem('SSOADMIN');
    if (isAd == '1' && this.tkn.trim().length > 0) {
    } else {
      localStorage.setItem('SSOUSERNAME', '');
      localStorage.setItem('SSOUSERPIC', '');
      this._router.navigateByUrl('/login');
    }
    this.userName = localStorage.getItem('SSOUSERNAME');
    this.picUrl = localStorage.getItem('SSOUSERPIC');

    // Check if we should show image initially
    this.showImage = !!(this.picUrl && this.picUrl.trim());
    this.getAllModules();

    // Subscribe to global search
    this._globalSearchService.searchContext$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(context => {
        if (context.currentRoute.includes('/app/dashboard')) {
          this.onGlobalSearch(context.searchTerm);
        }
      });
  }

  getAllModules(): void {
    this.roleId = localStorage.getItem('SSOUSERID');
    const gridDataReq: userIDDto = {
      id: this.roleId,
      UserEmail: ""
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._moduleApi.getUserModules(obj)
      .subscribe(
        (data: any) => {
          this.moduleList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c: ModuleList = {
              moduleId: _obj.moduleId,
              moduleDesc: _obj.moduleDesc,
              URL: _obj.URL,
              Description : _obj.Description,
              Applications: _obj.Applications ? _obj.Applications.map(app => ({
                AppName: app.AppName,
                AppLink: app.AppLink,
                AppPlatform: app.AppPlatform
              })) : []
            };
            this.moduleList.push(_c);
          }
          this.originalModuleList = [...this.moduleList]; // Store original data for search
        },
        _error => {
        }
      );
  }

  navigateToHousekeeping(url): void {
    window.location.href = url + '/login?token=' + this.tkn;
  }

  /**
     * Handle image load error - show initials instead
     */
  onImageError(): void {
    this.showImage = false;
  }



  /**
   * Handle successful image load
   */
  onImageLoad(): void {
    this.showImage = true;
  }

  /**
   * Get initials from user name (first letter of first name and last name)
   */
  getInitials(): string {
    if (!this.userName) {
      return '??';
    }

    const names = this.userName.trim().split(' ').filter(name => name.length > 0);

    if (names.length === 0) {
      return '??';
    } else if (names.length === 1) {
      // Only one name, take first two characters
      return names[0].substring(0, 2).toUpperCase();
    } else {
      // Take first letter of first name and first letter of last name
      const firstInitial = names[0].charAt(0);
      const lastInitial = names[names.length - 1].charAt(0);
      return (firstInitial + lastInitial).toUpperCase();
    }
  }

  // Card back side methods
  onCardFlip(module: ModuleList): void {
    const moduleId = module.moduleId.toString();

    // Initialize module data if not exists
    if (!this.moduleCardData[moduleId]) {
      this.moduleCardData[moduleId] = {
        selectedApp: null,
        selectedLink: ''
      };
    }

    // If there's only one application, auto-select it
    if (module.Applications && module.Applications.length === 1) {
      this.moduleCardData[moduleId].selectedApp = module.Applications[0];
      this.moduleCardData[moduleId].selectedLink = module.Applications[0].AppLink;
    } else if (module.Applications && module.Applications.length > 1) {
      // Auto-select first app for multi-app modules
      this.moduleCardData[moduleId].selectedApp = module.Applications[0];
      this.moduleCardData[moduleId].selectedLink = module.Applications[0].AppLink;
    }
  }

  getSelectedApp(module: ModuleList): any {
    const moduleId = module.moduleId.toString();
    return this.moduleCardData[moduleId] && this.moduleCardData[moduleId].selectedApp ? this.moduleCardData[moduleId].selectedApp : null;
  }

  getSelectedLink(module: ModuleList): string {
    const moduleId = module.moduleId.toString();
    return this.moduleCardData[moduleId] && this.moduleCardData[moduleId].selectedLink ? this.moduleCardData[moduleId].selectedLink : '';
  }

  onAppSelectionChange(module: ModuleList, app: any): void {
    const moduleId = module.moduleId.toString();
    if (!this.moduleCardData[moduleId]) {
      this.moduleCardData[moduleId] = {
        selectedApp: null,
        selectedLink: ''
      };
    }
    this.moduleCardData[moduleId].selectedApp = app;
    this.moduleCardData[moduleId].selectedLink = app && app.AppLink ? app.AppLink : '';
  }

  /**
   * Implementation of SearchableComponent interface
   * @param searchTerm The search term from global search
   */
  onGlobalSearch(searchTerm: string): void {
    if (!searchTerm || searchTerm.trim() === '') {
      // Reset to original data when search is cleared
      this.moduleList = [...this.originalModuleList];
    } else {
      // Filter data using the global search service
      this.moduleList = this._globalSearchService.filterData(
        this.originalModuleList,
        searchTerm,
        this.searchConfig
      );
    }
  }

  /**
   * Component cleanup
   */
  ngOnDestroy(): void {
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }
}
