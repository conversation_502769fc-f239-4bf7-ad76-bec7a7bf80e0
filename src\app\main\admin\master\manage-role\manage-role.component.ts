import { Component, OnInit, ViewChild, On<PERSON><PERSON>roy } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatSnackBar } from '@angular/material/snack-bar';
import { GlobalSearchService, SearchableComponent, SearchConfiguration } from 'app/services/global-search.service';
import { UserApiService } from 'app/services/api/user.api.service';
import { BaseComponent } from 'app/main/common/base.component';
import { RoleResponseDto, SaveRoleRequestDto } from 'app/dto/userDto';
import { GridFacilitySearchRequestDto, GridSearchRequestDto } from 'app/dto/common/gridDto';
import { FacilityApiService } from 'app/services/api/facility.api.service.service';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { RoleService } from 'app/services/api/role.service';
import { error } from 'console';

@Component({
  selector: 'app-manage-role',
  templateUrl: './manage-role.component.html',
  styleUrls: ['./manage-role.component.scss']
})
export class ManageRoleComponent extends BaseComponent implements OnInit, OnDestroy, SearchableComponent {
  @ViewChild('formRole', { static: true }) ngForm;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  search_role = '';
  fgRole = new FormGroup({
    roleId: new FormControl(0),
    roleName: new FormControl('', [Validators.required]),
    active: new FormControl(true),
    roleType: new FormControl('Ops', [Validators.required]),
    facility: new FormControl('', [Validators.required])
  });
  selectedRowIndex = 0;
  displayedColumns: string[] = ['roleName', 'active', 'roleType', 'actions',];
  dataSource: MatTableDataSource<RoleResponseDto>;
  roleGridData: RoleResponseDto[] = [];
  length = 0;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageEvent: PageEvent;
  shouldBeReadonly = '';
  facilities: any[] = [];
  facilityFilterCtrl: FormControl = new FormControl();
  filteredFacilityList: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  private _onDestroy = new Subject<void>();
  lastFacilityIdSelected: number;

  // Search functionality
  private _unsubscribeAll: Subject<any> = new Subject<any>();
  private originalRoleData: RoleResponseDto[] = [];
  private searchConfig: SearchConfiguration = {
    searchableFields: ['roleName', 'roleType'],
    caseSensitive: false,
    exactMatch: false,
    minSearchLength: 0
  };

  constructor(private _roleApi: RoleService,
    private _facilityApi: FacilityApiService,
    _snackBar: MatSnackBar,
    private _globalSearchService: GlobalSearchService) {
    super(_snackBar);
  }

  ngOnInit() {
    this.getActiveFacilityList();
    this.facilityFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterFacilityList();
      });

    // Subscribe to global search
    this._globalSearchService.searchContext$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(context => {
        if (context.currentRoute.includes('/app/role')) {
          this.onGlobalSearch(context.searchTerm);
        }
      });
  }

  ngOnDestroy() {
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private filterFacilityList() {
    if (!this.facilities) return;
    let search = this.facilityFilterCtrl.value;
    if (!search) {
      this.filteredFacilityList.next(this.facilities.slice());
      return;
    }
    search = search.toLowerCase();
    this.filteredFacilityList.next(
      this.facilities.filter(a =>
        a.value.toLowerCase().includes(search)
      )
    );
  }

  getAllRoles(): void {
    const gridDataReq: GridFacilitySearchRequestDto = {
      page: 0,
      pageSize: this.pageSize,
      searchString: this.search_role,
      FacilityId: this.lastFacilityIdSelected ? this.lastFacilityIdSelected : 0
    };
    this.length = 0;
    const obj = this.wrapRequestObject(gridDataReq);
    this._roleApi.getAllRoles(obj)
      .subscribe(
        (data) => {
          this.roleGridData = [];
          const objArray = JSON.parse(data.obj.toString());
          this.length = objArray ? objArray.totalRecords : 0;
          if (objArray && objArray['gridData']) {
            for (const _obj of objArray['gridData']) {
              const _r: RoleResponseDto = {
                roleId: _obj.roleId,
                roleName: _obj.roleName,
                active: _obj.active,
                roleType: _obj.roleType,
                facility: _obj.facilityId,
              };
              this.roleGridData.push(_r);
            }
          }
          this.originalRoleData = [...this.roleGridData]; // Store original data for search
          this.dataSource = new MatTableDataSource(this.roleGridData);
          this.dataSource.sort = this.sort;
          this.paginator.firstPage();
          this.shouldBeReadonly = '';
        },
        _error => {
        }
      );
  }

  pagerEvent(event?: PageEvent): void {
    const gridDataReq: GridSearchRequestDto = {
      page: event.pageIndex,
      pageSize: event.pageSize,
      searchString: this.search_role,
      UserEmail: ''
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._roleApi.getAllRoles(obj)
      .subscribe(
        (data) => {
          this.roleGridData = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray['gridData']) {
            const _r: RoleResponseDto = {
              roleId: _obj.roleId,
              roleName: _obj.roleName,
              active: _obj.active,
              roleType: _obj.roleType,
              facility: _obj.facilityId,
            };
            this.roleGridData.push(_r);
          }
          this.dataSource = new MatTableDataSource(this.roleGridData);
          this.dataSource.sort = this.sort;
        },
        _error => {
        }
      );
  }

  resetExtraDetails(): void {
    this.search_role = '';
    this.ngForm.resetForm();
    this.selectedRowIndex = 0;
    this.getAllRoles();
    this.pageSize = 5;
    this.shouldBeReadonly = '';
    this.fgRole.controls['facility'].enable();
  }

  loadRoleDetails(row): void {
    this.shouldBeReadonly = 'true';
    this.selectedRowIndex = row.roleId;
    this.fgRole.controls['facility'].disable();
    this.fgRole.setValue({
      roleId: row.roleId,
      roleName: row.roleName,
      active: row.active,
      roleType: row.roleType,
      facility: row.facility
    });
  }

  saveRole(): void {
    const roleObj: SaveRoleRequestDto = {
      roleId: (this.fgRole.controls.roleId && this.fgRole.controls.roleId.value) ? Number(this.fgRole.controls.roleId.value) : 0,
      roleName: this.fgRole.controls.roleName.value,
      active: this.fgRole.controls.active.value,
      roleType: this.fgRole.controls.roleType.value,
      facilityId: Number(this.fgRole.controls.facility.value),
      createdBy: Number(localStorage.getItem('SSOUSERID')),
    };
    const obj = this.wrapRequestObject(roleObj);
    this._roleApi.saveRole(obj)
      .subscribe(
        (data) => {
          if (data.code === 1) {
            this.showSuccessMsg('Changes have been saved successfully');
            this.lastFacilityIdSelected = Number(this.fgRole.controls.facility.value)
            this.ngForm.resetForm();
            this.selectedRowIndex = 0;
            this.pageSize = 5;
            this.fgRole.controls['facility'].enable();
            this.getAllRoles();
          } else {
            this.showErrorMsg(data.msg);
          }
        },
        _error => {
          this.showErrorMsg(_error);
        }
      );
  }

  deleteRole(row): void {
    if (confirm('Are you sure you want to delete this role?')) {
      const roleId = row.roleId;
      const obj = this.wrapRequestObject({ roleId });
      this._roleApi.deleteRole(obj)
        .subscribe(
          (data) => {
            if (data.code === 1) {
              this.showSuccessMsg('Role deleted successfully');
              this.getAllRoles();
            } else {
              this.showErrorMsg(data.msg);
            }
          },
          _error => {
          }
        );
    }
  }
  onFacilityChange(): void {
    this.lastFacilityIdSelected = Number(this.fgRole.controls.facility.value)
    this.getAllRoles();
  }

  getActiveFacilityList(): void {
    this._facilityApi.getActiveFacilityList()
      .subscribe(
        (data: any) => {
          this.facilities = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c = {
              code: _obj.Code,
              value: _obj.Value
            };
            this.facilities.push(_c);
          }
          this.filteredFacilityList.next(this.facilities.slice());
        },
        _error => {
        }
      );
  }
}
