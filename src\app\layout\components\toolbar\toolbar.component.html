<mat-toolbar class="p-0 mat-elevation-z1">

    <div fxFlex fxLayout="row" fxLayoutAlign="start center" style="margin: 0 15px;">

        <div fxFlex="1 0 auto" fxLayout="row" fxLayoutAlign="start center">

            <button mat-icon-button class="navbar-toggle-button" (click)="toggleSidebarOpen('navbar')">
                <mat-icon class="secondary-text">{{ sidebarVisible ? 'menu_open' : 'menu' }}</mat-icon>
            </button>

            <!-- <div fxLayout="row" fxLayoutAlign="start center" *ngIf="horizontalNavbar">
                <div class="logo ml-16">
                    <img class="logo-icon" src="assets/images/logos/UETrackLogo2020.png">
                </div>
            </div>
            <div class="px-8 px-md-16">
                    <blockquote>
                <span class="mat-headline"><cite>UE</cite>Track&trade; SSO Service</span>
                    </blockquote>
            </div> -->

        </div>
        <div fxLayout="row" fxLayoutAlign="center center" class="search-container">
            <button mat-icon-button *ngIf="!searchActive" (click)="toggleSearch()" class="search-toggle-button">
                <mat-icon style="color: #64748B;">search</mat-icon>
            </button>

            <div *ngIf="searchActive" fxLayout="row" fxLayoutAlign="start center" class="search-input-container">
                <mat-form-field appearance="outline" class="search-input">
                    <mat-icon style="color: #64748B;">search</mat-icon>
                    <input matInput
                           placeholder="Search across all modules..."
                           [formControl]="searchControl"
                           (keyup)="onSearchKeyup($event)"
                           #searchInput>
                </mat-form-field>
                <button mat-icon-button (click)="toggleSearch()" class="search-close-button">
                    <mat-icon style="color: #64748B;">close</mat-icon>
                </button>
            </div>
        </div>
        <div
            style="display: flex; align-items: center; gap: 8px; font-size: 14px; border: 1px solid #ccc; padding: 4px 8px; border-radius: 6px;">
            <mat-icon style="color: #64748B;">domain</mat-icon>
            <span>Facility</span>
            <mat-icon style="color: #64748B;">arrow_drop_down</mat-icon>
        </div>
        <!-- <div class="" fxFlex="0 1 auto" fxLayout="row" fxLayoutAlign="start center"> -->

        <!-- <button mat-button [matMenuTriggerFor]="userMenu"
                    class="user-button">
                <div fxLayout="row" fxLayoutAlign="center center">
                    <img class="avatar mr-0 mr-sm-16" [src]="picUrl">
                    <span class="username mr-12" fxHide fxShow.gt-sm>{{userName}}</span>
                    <mat-icon class="s-16" fxHide.xs>keyboard_arrow_down</mat-icon>
                </div>
            </button> -->

        <!-- <mat-menu #userMenu="matMenu" [overlapTrigger]="false">

                 <button mat-menu-item  (click)="changepassword()">
                    <mat-icon>vpn_key</mat-icon>
                    <span>Change Password</span>
                </button>


                <button mat-menu-item class="" (click)="logout()">
                    <mat-icon>exit_to_app</mat-icon>
                    <span>Logout</span>
                </button>

            </mat-menu> -->

        <!-- <div class="toolbar-separator"></div>

            <fuse-search-bar (input)="search($event)"></fuse-search-bar>

            <div class="toolbar-separator"></div>

            <button mat-button fxHide fxShow.gt-xs
                    class="language-button"
                    [matMenuTriggerFor]="languageMenu">
                <div fxLayout="row" fxLayoutAlign="center center">
                    <img class="flag mr-8" [src]="'assets/icons/flags/'+selectedLanguage.flag+'.png'">
                    <span class="iso text-uppercase">{{selectedLanguage.id}}</span>
                </div>
            </button>

            <mat-menu #languageMenu="matMenu" [overlapTrigger]="false">

                <button mat-menu-item *ngFor="let lang of languages" (click)="setLanguage(lang)">
                    <span fxLayout="row" fxLayoutAlign="start center">
                        <img class="flag mr-16" [src]="'assets/icons/flags/'+lang.flag+'.png'">
                        <span class="iso">{{lang.title}}</span>
                    </span>
                </button>

            </mat-menu>

            <div class="toolbar-separator" fxHide fxShow.gt-xs></div>

            <button mat-icon-button
                    class="quick-panel-toggle-button"
                    (click)="toggleSidebarOpen('quickPanel')"
                    aria-label="Toggle quick panel">
                <mat-icon class="secondary-text">format_list_bulleted</mat-icon>
            </button>

            <div class="toolbar-separator" *ngIf="!hiddenNavbar && rightNavbar" fxHide fxShow.gt-xs></div>

            <button mat-icon-button class="navbar-toggle-button"
                    *ngIf="!hiddenNavbar && rightNavbar" (click)="toggleSidebarOpen('navbar')" fxHide.gt-md>
                <mat-icon class="secondary-text">menu</mat-icon>
            </button> -->

        <!-- </div> -->

    </div>

</mat-toolbar>