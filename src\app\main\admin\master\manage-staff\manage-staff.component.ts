import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GlobalSearchService, SearchableComponent, SearchConfiguration } from 'app/services/global-search.service';
import { StaffApiService } from 'app/services/api/staff.api.service';
import { StaffResponseDto } from 'app/core/models/staff-response.dto';
import { SaveStaffRequestDto } from 'app/core/models/save-staff-request.dto';
import { ApiRequest } from 'app/dto/common/commonDto';
import { UserApiService } from 'app/services/api/user.api.service';
import { RoleService } from 'app/services/api/role.service';
import { BaseComponent } from 'app/main/common/base.component';
import { MatSnackBar } from '@angular/material';
import { FacilityApiService } from 'app/services/api/facility.api.service.service';
import { GridFacilitySearchRequestDto } from 'app/dto/common/gridDto';
import { DomSanitizer } from '@angular/platform-browser';
import { CountryISO, SearchCountryField } from 'ngx-intl-tel-input';
import { MatDialog } from '@angular/material';
import { FileuploaderComponent } from 'app/main/common/fileuploader/fileuploader.component';
import * as XLSX from 'xlsx';
import { ResignationDateDialogComponent } from 'app/main/admin/master/manage-staff/resignation-date-dialog/resignation-date-dialog.component';
import { ConfirmDialogComponent } from 'app/main/admin/master/manage-staff/confirm-dialog/confirm-dialog.component';
import * as moment from 'moment';

@Component({
    selector: 'app-manage-staff',
    templateUrl: './manage-staff.component.html',
    styleUrls: ['./manage-staff.component.scss']
})
export class ManageStaffComponent extends BaseComponent implements OnInit, OnDestroy, SearchableComponent {
    @ViewChild('formStaff', { static: true }) ngForm;
    @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
    @ViewChild(MatSort, { static: true }) sort: MatSort;

    fgStaff: FormGroup;
    facilityFilterCtrl: FormControl = new FormControl();
    filteredFacilityList: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    protected _onDestroy = new Subject<void>();

    // Phone input configuration
    separateDialCode = true;
    SearchCountryField = SearchCountryField;
    CountryISO = CountryISO;
    preferredCountries: CountryISO[] = [CountryISO.Singapore, CountryISO.UnitedStates, CountryISO.UnitedKingdom, CountryISO.India];

    displayedColumns: string[] = ['employeeId', 'employeeName', 'emailAddress', 'phone', 'photo', 'roleName', 'actions'];
    dataSource: MatTableDataSource<StaffResponseDto>;
    length = 0;
    pageSize = 5;
    page = 0;
    pageSizeOptions: number[] = [5, 10, 25, 100];
    countryCodeList: number[] = [+65, +91, +44, +1];

    selectedRowIndex: number = -1;
    search_staff: string = '';
    roles: any[] = [];
    roleList: any[] = [];
    facilities: any[];
    lastFacilityIdSelected: number = 0;
    selectedPhoto: File | null = null;
    photoPreview: string | null = null;
    isOpsRole: boolean = false;
    isEmployeeIdEditable: boolean = false;

    // Search functionality
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    private originalStaffData: StaffResponseDto[] = [];
    private searchConfig: SearchConfiguration = {
        searchableFields: ['employeeId', 'employeeName', 'emailAddress', 'roleName'],
        caseSensitive: false,
        exactMatch: false,
        minSearchLength: 0
    };

    constructor(
        private _formBuilder: FormBuilder,
        private _staffApiService: StaffApiService,
        private _userApiService: UserApiService,
        private _facilityApiService: FacilityApiService,
        private _roleApiService: RoleService,
        private _sanitizer: DomSanitizer,
        private _dialog: MatDialog,
        _snackBar: MatSnackBar,
        private _globalSearchService: GlobalSearchService
    ) {
        super(_snackBar);
        this.fgStaff = this._formBuilder.group({
            facility: ['', Validators.required],
            employeeId: ['', Validators.required],
            employeeName: ['', Validators.required],
            emailAddress: [''],
            countryCode: ['', Validators.required],
            phoneNumber: ['', [
                Validators.required,
            ]],
            roleId: ['', Validators.required],
            dateOfJoining: ['', Validators.required],
            nationality: [''],
            postalCode: [''],
            gender: ['Male'],
            isProfileLocked: [false],
            photo: [null],
            isActive: [false],
        });
    }

    ngOnInit(): void {
        this.getLastEmployeeId();
        this.loadFacilities();
        this.facilityFilterCtrl.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                this.filterFacilityList();
            });
        this.fgStaff.get('employeeId').disable();

        // Subscribe to global search
        this._globalSearchService.searchContext$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(context => {
                if (context.currentRoute.includes('/app/staff')) {
                    this.onGlobalSearch(context.searchTerm);
                }
            });
    }

    loadFacilities(): void {
        this._facilityApiService.getActiveFacilityList()
            .subscribe(
                (data: any) => {
                    this.facilities = [];
                    const objArray = JSON.parse(data.obj.toString());
                    for (const _obj of objArray) {
                        const _c = {
                            code: _obj.Code,
                            value: _obj.Value
                        };
                        this.facilities.push(_c);
                    }
                    this.filteredFacilityList.next(this.facilities.slice());
                },
                _error => {
                }
            );
    }

    private filterFacilityList() {
        if (!this.facilities) return;
        let search = this.facilityFilterCtrl.value;
        if (!search) {
            this.filteredFacilityList.next(this.facilities.slice());
            return;
        }
        search = search.toLowerCase();
        this.filteredFacilityList.next(
            this.facilities.filter(a =>
                a.value.toLowerCase().includes(search)
            )
        );
    }

    onFacilityChange(): void {
        this.lastFacilityIdSelected = Number(this.fgStaff.get('facility').value);
        if (this.fgStaff.get('facility').value) {
            this.page = 0;
            this.pageSize = 5;
            this.length = 0;
            this.getAllStaffs();
            this.loadRoles();
        }
    }

    getLastEmployeeId(): void {
        this._staffApiService.getLastEmployeeId().subscribe(
            (response) => {
                if (response.code === 1) {
                    let lastEmployeeId = JSON.parse(response.obj.toString());
                    if (lastEmployeeId && lastEmployeeId.startsWith('TEMP')) {
                        const numPart = parseInt(lastEmployeeId.substring(4));
                        lastEmployeeId = 'TEMP' + String(numPart + 1).padStart(3, '0');
                    }
                    this.fgStaff.patchValue({
                        employeeId: lastEmployeeId
                    });
                } else {
                    this.fgStaff.patchValue({
                        employeeId: 'TEMP001'
                    });
                }
            }
        );
    }

    loadRoles(): void {
        this.roles = [];
        this.roleList = [];
        const obj = this.wrapRequestObject(this.fgStaff.controls['facility'].value);
        this._roleApiService.getActiveRoleList(obj).subscribe(
            (response) => {
                if (response.code === 1) {
                    const objArray = JSON.parse(response.obj.toString());
                    for (const _obj of objArray) {
                        const _c = {
                            code: _obj.roleId,
                            value: _obj.roleName
                        };
                        this.roleList.push(_c);
                        this.roles.push(_obj);
                    }
                } else {

                    this.showErrorMsg(response.msg != '' && response.msg != null ? response.msg : 'No roles found for selected facility.');
                }
            }
        );
    }

    getAllStaffs(): void {
        const gridDataReq: GridFacilitySearchRequestDto = {
            page: this.page,
            pageSize: this.pageSize,
            searchString: this.search_staff,
            FacilityId: this.lastFacilityIdSelected ? this.lastFacilityIdSelected : 0
        };
        const obj = this.wrapRequestObject(gridDataReq);
        this._staffApiService.getAllStaffs(obj).subscribe(
            (response) => {
                if (response.code === 1) {
                    const objArray = JSON.parse(response.obj.toString());
                    this.length = objArray != null ? objArray.totalRecords : 0;
                    const staffData = objArray['gridData'] || [];
                    this.originalStaffData = [...staffData]; // Store original data for search
                    this.dataSource = new MatTableDataSource(staffData);
                    this.dataSource.sort = this.sort;
                } else {
                    this.originalStaffData = [];
                    this.dataSource = new MatTableDataSource([]);
                }
            }
        );
    }

    onPhotoSelected(event: Event): void {
        const file = (event.target as HTMLInputElement).files[0];
        if (file) {
            this.selectedPhoto = file;
            const reader = new FileReader();
            reader.onload = (e: any) => {
                this.photoPreview = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    }

    uploadPhoto(): boolean {
        const dialogRef = this._dialog.open(FileuploaderComponent, {
            width: '400px',
            data: {
                name: 'Change Staff Photo',
                filePath: '',
                base64: this.fgStaff.controls['photo'].value
            }
        });

        dialogRef.afterClosed().subscribe(result => {
            if (result.data && result.data.length > 0) {
                this.fgStaff.controls.photo.setValue(result.data);
                this.photoPreview = result.data;
            } else {
                this.fgStaff.controls.photo.setValue(null);
                this.photoPreview = null;
            }
        });
        return false;
    }

    removePhoto(): void {
        this.fgStaff.controls.photo.setValue(null);
        this.photoPreview = null;
    }

    saveStaff(): void {
        if (this.fgStaff.invalid) {
            return;
        }
        const contactNumber = this.fgStaff.get('countryCode').value + ' ' + this.fgStaff.get('phoneNumber').value;

        const request: SaveStaffRequestDto = {
            staffId: this.selectedRowIndex,
            facilityId: this.fgStaff.get('facility').value,
            employeeId: this.fgStaff.get('employeeId').value,
            employeeName: this.fgStaff.get('employeeName').value,
            emailAddress: this.fgStaff.get('emailAddress').value == '' ? null : this.fgStaff.get('emailAddress').value,
            contactNumber: contactNumber,
            roleId: this.fgStaff.get('roleId').value,
            dateOfJoining: this.pareseDateTime(this.fgStaff.get('dateOfJoining').value),
            nationality: this.fgStaff.get('nationality').value,
            postalCode: this.fgStaff.get('postalCode').value ? Number(this.fgStaff.get('postalCode').value) : null,
            gender: this.fgStaff.get('gender').value,
            isProfileLocked: this.fgStaff.get('isProfileLocked').value,
            isActive: this.fgStaff.get('isActive').value ? true : false,
            createdBy: Number(localStorage.getItem('SSOUSERID')),
            photo: this.fgStaff.get('photo').value,
            photoPath: undefined,
            resignationDate: undefined,
        };

        const obj = this.wrapRequestObject(request);
        this._staffApiService.saveStaff(obj).subscribe(
            (response) => {
                if (response.code === 1) {
                    this.showSuccessMsg('Staff saved successfully');
                    this.lastFacilityIdSelected = Number(this.fgStaff.get('facility').value);
                    const facilityValue = this.fgStaff.get('facility').value;
                    this.ngForm.resetForm();
                    this.fgStaff.patchValue({
                        facility: facilityValue,
                        gender: 'Male',
                        isProfileLocked: false,
                        photo: null
                    });
                    this.selectedRowIndex = -1;
                    this.selectedPhoto = null;
                    this.photoPreview = null;
                    this.getAllStaffs();
                    this.getLastEmployeeId();
                } else {
                    this.showErrorMsg(response.msg);
                }
            }
        );
    }

    loadStaffDetails(staff: StaffResponseDto): void {
        this.selectedRowIndex = staff.staffId;
        let contactNumber = staff.contactNumber;
        let countryCode = '';
        let phoneNumber = '';

        if (contactNumber && contactNumber.startsWith('+')) {
            const firstSpaceIdx = contactNumber.indexOf(' ');
            if (firstSpaceIdx > 0) {
                countryCode = contactNumber.substring(0, firstSpaceIdx);
                phoneNumber = contactNumber.substring(firstSpaceIdx + 1);
            } else {
                countryCode = contactNumber;
                phoneNumber = '';
            }
        } else {
            phoneNumber = contactNumber || '';
        }

        this.fgStaff.patchValue({
            facility: staff.facilityId,
            employeeId: staff.employeeId,
            employeeName: staff.employeeName,
            emailAddress: staff.emailAddress == null ? '' : staff.emailAddress,
            countryCode: countryCode,
            phoneNumber: phoneNumber,
            roleId: staff.roleId,
            dateOfJoining: staff.dateOfJoining,
            nationality: staff.nationality,
            postalCode: staff.postalCode,
            gender: staff.gender,
            isActive: staff.isActive,
            isProfileLocked: staff.isProfileLocked,
            photo: staff.photoPath
        });
    }

    deleteStaff(staff: StaffResponseDto): void {
        const dialogRef = this._dialog.open(ResignationDateDialogComponent, {
            width: '400px',
            disableClose: true
        });

        dialogRef.afterClosed().subscribe(result => {
            if (result) {
                const staffId = staff.staffId;
                const obj = this.wrapRequestObject({
                    staffId,
                    resignationDate: this.pareseDateTime(result),
                    createdBy: Number(localStorage.getItem('SSOUSERID')),
                });
                this._staffApiService.deleteStaff(obj).subscribe(
                    (response) => {
                        if (response.code === 1) {
                            this.showSuccessMsg('Staff deleted successfully');
                            this.lastFacilityIdSelected = Number(this.fgStaff.get('facility').value);
                            this.dataSource = new MatTableDataSource([]);
                            this.page = 0;
                            this.pageSize = 5;
                            this.length = 0;
                            this.paginator.pageIndex = 0;
                            this.getAllStaffs();
                        } else {
                            this.showErrorMsg(response.msg);
                        }
                    }
                );
            }
        });
    }

    resetExtraDetails(): void {
        this.dataSource = new MatTableDataSource([]);
        this.page = 0;
        this.pageSize = 5;
        this.length = 0;
        this.selectedRowIndex = -1;
        this.ngForm.resetForm();
        this.selectedPhoto = null;
        this.photoPreview = null;
        setTimeout(() => {
            this.fgStaff.patchValue({
                gender: 'Male',
                isProfileLocked: false,
                photo: null
            });
        });
        this.getLastEmployeeId();
    }

    pagerEvent(event: PageEvent): void {
        this.page = event.pageIndex;
        this.pageSize = event.pageSize;
        this.getAllStaffs();
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }


    // onRoleChange(): void {
    //     if (this.fgStaff.get('roleId').value) {
    //         const selectedRole = this.roles.find(role => role.roleId === this.fgStaff.get('roleId').value);
    //         this.isOpsRole = selectedRole.roleType.toLowerCase().includes('non-ops') || false;
    //         if (this.isOpsRole) {
    //             this.fgStaff.get('emailAddress').enable();
    //         }
    //         else {
    //             this.fgStaff.get('emailAddress').disable();
    //             this.fgStaff.get('emailAddress').setValue('');
    //         }
    //     }
    // }

    importStaff(): void {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx, .xls';
        input.onchange = (event: any) => {
            const file = event.target.files[0];
            const reader = new FileReader();
            reader.onload = (e: any) => {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

                // Convert sheet to JSON with header row
                const jsonData = XLSX.utils.sheet_to_json(firstSheet, {
                    header: ['employeeId', 'employeeName', 'emailAddress', 'contactNumber', 'roleId', 'dateOfJoining', 'nationality', 'postalCode', 'gender', 'isActive', 'isProfileLocked'],
                    range: 1 // Skip the header row
                });

                // Process each staff record
                jsonData.forEach((staff: any) => {
                    const staffData: SaveStaffRequestDto = {
                        facilityId: this.fgStaff.get('facility').value,
                        employeeId: staff.employeeId,
                        employeeName: staff.employeeName,
                        emailAddress: staff.emailAddress || '',
                        contactNumber: staff.contactNumber,
                        roleId: parseInt(staff.roleId),
                        dateOfJoining: staff.dateOfJoining ? this.pareseDateTime(staff.dateOfJoining) : undefined,
                        nationality: staff.nationality || '',
                        postalCode: staff.postalCode ? Number(staff.postalCode) : undefined,
                        gender: staff.gender || 'Male',
                        isProfileLocked: staff.isProfileLocked ? staff.isProfileLocked.toLowerCase() === 'yes' : false,
                        isActive: staff.isActive ? staff.isActive.toLowerCase() === 'yes' : false,
                        photo: null,
                        createdBy: Number(localStorage.getItem('SSOUSERID')),
                        photoPath: undefined,
                        resignationDate: undefined,
                        staffId: undefined
                    };

                    this._staffApiService.saveStaff(this.wrapRequestObject(staffData)).subscribe(
                        (response) => {
                            if (response.code === 1) {
                                this.showSuccessMsg('Staff imported successfully');
                                this.getAllStaffs();
                                this.getLastEmployeeId();
                            } else {
                                this.showErrorMsg(response.msg);
                            }
                        }
                    );
                });
            };
            reader.readAsArrayBuffer(file);
        };
        input.click();
    }

    exportStaff(): void {
        if (!this.dataSource || !this.dataSource.data.length) {
            this.showErrorMsg('No staff data to export');
            return;
        }

        const exportData = this.dataSource.data.map(staff => ({
            'Employee ID': staff.employeeId,
            'Employee Name': staff.employeeName,
            'Email Address': staff.emailAddress,
            'Contact Number': staff.contactNumber,
            'Role': staff.roleName,
            'Date of Joining': staff.dateOfJoining,
            'Nationality': staff.nationality,
            'Postal Code': staff.postalCode,
            'Gender': staff.gender,
            'Active': staff.isActive ? 'Yes' : 'No',
            'Profile Locked': staff.isProfileLocked ? 'Yes' : 'No'
        }));

        const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(exportData);
        const workbook: XLSX.WorkBook = { Sheets: { 'Staff List': worksheet }, SheetNames: ['Staff List'] };

        // Generate Excel file
        XLSX.writeFile(workbook, 'staff_list.xlsx');
    }

    downloadImportTemplate(): void {
        // Create sample data for the template
        const templateData = [
            {
                'Employee ID': 'EMP001',
                'Employee Name': 'John Doe',
                'Email Address': '<EMAIL>',
                'Contact Number': '+65 9123 4567',
                'Role ID': '1',
                'Date of Joining': '2024-01-01',
                'Nationality': 'Singapore',
                'Postal Code': '123456',
                'Gender': 'Male',
                'Active': 'Yes',
                'Profile Locked': 'No'
            },
            {
                'Employee ID': 'EMP002',
                'Employee Name': 'Jane Smith',
                'Email Address': '<EMAIL>',
                'Contact Number': '+65 9876 5432',
                'Role ID': '2',
                'Date of Joining': '2024-01-15',
                'Nationality': 'Malaysia',
                'Postal Code': '54321',
                'Gender': 'Female',
                'Active': 'Yes',
                'Profile Locked': 'No'
            }
        ];

        // Create worksheet with the template data
        const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(templateData);

        // Add column descriptions in the first row
        const descriptions = {
            'A1': 'Required: Unique identifier for the employee',
            'B1': 'Required: Full name of the employee',
            'C1': 'Required: Valid email address',
            'D1': 'Required: Contact number with country code',
            'E1': 'Required: Role ID from the system',
            'F1': 'Optional: Date in YYYY-MM-DD format',
            'G1': 'Optional: Employee nationality',
            'H1': 'Optional: Postal Code',
            'I1': 'Required: Male or Female',
            'J1': 'Optional: Active (Yes or No)',
            'K1': 'Optional: Profile Lock (Yes or No)',

        };

        // Add descriptions to the worksheet
        Object.entries(descriptions).forEach(([cell, value]) => {
            worksheet[cell] = { t: 's', v: value };
        });

        // Create workbook with the worksheet
        const workbook: XLSX.WorkBook = {
            Sheets: { 'Staff Import Template': worksheet },
            SheetNames: ['Staff Import Template']
        };

        // Generate and download the template file
        XLSX.writeFile(workbook, 'staff_import_template.xlsx');
    }

    onEmployeeIdEditChange(): void {
        if (!this.isEmployeeIdEditable) {
            this.fgStaff.get('employeeId').disable();
        } else {
            this.fgStaff.get('employeeId').enable();
        }
    }
    pareseDateTime(date) {
        //resolving the utc time issues
        return moment(date).add(moment(date).utcOffset(), 'm').utc();
    }

    /**
     * Implementation of SearchableComponent interface
     * @param searchTerm The search term from global search
     */
    onGlobalSearch(searchTerm: string): void {
        if (!searchTerm || searchTerm.trim() === '') {
            // Reset to original data when search is cleared
            const staffData = [...this.originalStaffData];
            this.dataSource = new MatTableDataSource(staffData);
        } else {
            // Filter data using the global search service
            const filteredData = this._globalSearchService.filterData(
                this.originalStaffData,
                searchTerm,
                this.searchConfig
            );
            this.dataSource = new MatTableDataSource(filteredData);
        }

        // Update the data source
        this.dataSource.sort = this.sort;
        this.paginator.firstPage();
    }

    /**
     * Component cleanup
     */
    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        this._onDestroy.next();
        this._onDestroy.complete();
    }
}