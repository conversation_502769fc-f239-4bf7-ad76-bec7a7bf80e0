import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';

export interface SearchContext {
  searchTerm: string;
  currentRoute: string;
  timestamp: Date;
}

export interface SearchableComponent {
  onGlobalSearch(searchTerm: string): void;
}

export interface SearchResult {
  id: string | number;
  title: string;
  description?: string;
  type: string;
  data: any;
}

export interface SearchConfiguration {
  searchableFields: string[];
  caseSensitive?: boolean;
  exactMatch?: boolean;
  minSearchLength?: number;
}

@Injectable({
  providedIn: 'root'
})
export class GlobalSearchService {
  private searchSubject = new BehaviorSubject<SearchContext>({
    searchTerm: '',
    currentRoute: '',
    timestamp: new Date()
  });

  public searchContext$: Observable<SearchContext> = this.searchSubject.asObservable();

  constructor(private router: Router) {}

  /**
   * Emit a search term to all subscribed components
   * @param searchTerm The search term to broadcast
   */
  search(searchTerm: string): void {
    const context: SearchContext = {
      searchTerm: searchTerm.trim(),
      currentRoute: this.router.url,
      timestamp: new Date()
    };
    
    this.searchSubject.next(context);
  }

  /**
   * Clear the current search
   */
  clearSearch(): void {
    this.search('');
  }

  /**
   * Get the current search term
   */
  getCurrentSearchTerm(): string {
    return this.searchSubject.value.searchTerm;
  }

  /**
   * Check if there's an active search
   */
  hasActiveSearch(): boolean {
    return this.searchSubject.value.searchTerm.length > 0;
  }

  /**
   * Utility method to filter array data based on search configuration
   * @param data Array of data to search through
   * @param searchTerm Search term
   * @param config Search configuration
   */
  filterData<T>(data: T[], searchTerm: string, config: SearchConfiguration): T[] {
    if (!searchTerm || searchTerm.length < (config.minSearchLength || 1)) {
      return data;
    }

    const term = config.caseSensitive ? searchTerm : searchTerm.toLowerCase();

    return data.filter(item => {
      return config.searchableFields.some(field => {
        const fieldValue = this.getNestedProperty(item, field);
        if (fieldValue === null || fieldValue === undefined) {
          return false;
        }

        const stringValue = config.caseSensitive ?
          String(fieldValue) :
          String(fieldValue).toLowerCase();

        return config.exactMatch ?
          stringValue === term :
          stringValue.includes(term);
      });
    });
  }

  /**
   * Get nested property value from object using dot notation
   * @param obj Object to search in
   * @param path Property path (e.g., 'user.name' or 'address.city')
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, prop) => {
      return current && current[prop] !== undefined ? current[prop] : null;
    }, obj);
  }
}
