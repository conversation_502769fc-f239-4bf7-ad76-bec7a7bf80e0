:host {

    .content {
        padding: 10px 15px;
        background-color: #F1F5F9;

        form {
            width: 100%;
            // max-width: 800px !important;
        }

        .form-errors-model {
            flex: 1;

            code {
                background: none !important;
            }
        }

        .horizontal-stepper-wrapper,
        .vertical-stepper-wrapper {
            max-width: 800px;
        }

        .mat-stroked-button.mat-small {

            height: 25px;
            line-height: 20px;
            min-height: 20px;
            vertical-align: middle !important;
            font-size: 10px;
            margin-bottom: 33%;
        }

        .mat-stroked-button.mat-sm {

            height: 25px;
            line-height: 20px;
            min-height: 20px;
            vertical-align: middle !important;
            font-size: 10px;
        }

        .mat-flat-button.mat-top-margin {
            margin-top: 20px !important;
        }

        .mat-stroked-button.mat-top-margin {
            margin-top: 20px !important;
        }

        .h-30 {
            height: 30px !important;
            min-height: 30px;
        }

        table {
            width: 100%;
        }

        .mat-form-field.grid-search-field {
            // font-size: 14px;
            width: 50%;
            margin-left: 20px !important;
        }

        .blue-snackbar {
            background: #2196F3;
        }

    }
}
mat-form-field {
    width: 100%
}

// Cards Container
.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 10px;
    padding: 10px 0;

    // Responsive adjustments
    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 10px 0;
    }

    @media (min-width: 1200px) {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }

    // Large monitor adjustments (1440px and above)
    @media (min-width: 1440px) {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    // Extra large monitors (1920px and above)
    @media (min-width: 1920px) {
        grid-template-columns: repeat(5, 1fr);
        gap: 25px;
        max-width: 1400px;
        margin: 0 auto;
    }
}

// Flip Card Styling
.flip-card {
    height: 370px;
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
    perspective: 1000px;

    // Large monitor adjustments
    @media (min-width: 1440px) {
        height: 380px;
        max-width: 380px;
    }

    // Extra large monitors
    @media (min-width: 1920px) {
        height: 400px;
        max-width: 420px;
    }



    .fuse-widget-front {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;

        .card-content {
            height: 100%;
            display: flex;
            flex-direction: column;

            .image-section {
                height: 50%;
                overflow: hidden;
                position: relative;
                cursor: pointer;

                .module-image {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .content-section {
                height: 50%;
                padding: 20px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .module-title {
                    font-size: 20px;
                    font-weight: 700;
                    color: black;
                    margin: 0 0 8px 0;
                    text-transform: capitalize;
                    letter-spacing: 0.04em;
                }

                .module-description {
                    font-size: 14px;
                    font-weight: 300;
                    color: #666;
                    margin: 0 0 16px 0;
                    line-height: 1.4;
                    flex-grow: 1;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

                .read-more-btn {
                    background: transparent;
                    border: none;
                    color: #4144d8;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    padding: 4px 8px;
                    border-radius: 12px;
                    transition: background-color 0.2s ease;
                    display: inline-block;
                    width: fit-content;
                    margin-left: -8px;

                    &:hover {
                        background-color: #e0e7ff;
                    }
                }
            }
        }
    }

    .fuse-widget-back {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .back-content {
            height: 100%;
            padding: 16px;
            display: flex;
            flex-direction: column;
            position: relative;

            .back-btn {
                position: absolute;
                top: 12px;
                left: 12px;
                background: transparent;
                border: none;
                border-radius: 50%;
                width: 36px;
                height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: background-color 0.2s ease;
                z-index: 10;

                &:hover {
                    background: #f1f5f9;
                }

                mat-icon {
                    font-size: 20px;
                    color: #64748b;
                }
            }

            .back-main-content {
                margin-top: 40px;
                height: calc(100% - 40px);
                display: flex;
                flex-direction: column;
                padding: 0 8px;

                .back-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #1e293b;
                    margin: 0 0 8px 0;
                    text-align: center;
                    line-height: 1.3;
                }

                .app-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 6px;

                    .app-selector {
                        margin-bottom: 4px;

                        .mat-form-field {
                            width: 100%;
                            font-size: 14px;

                            .mat-form-field-label {
                                color: #64748b;
                                font-size: 13px;
                            }

                            .mat-select {
                                font-size: 14px;
                            }
                        }
                    }

                    .single-app-info {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: 12px;
                        flex: 1;
                        justify-content: flex-start;
                        padding: 8px 0 0 0;

                        .qr-instruction {
                            text-align: center;
                            font-size: 13px;
                            color: #475569;
                            font-weight: 500;
                            line-height: 1.4;
                            margin: 0 8px;
                        }

                        .qr-container {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            padding: 12px;
                            background: #f8fafc;
                            border-radius: 8px;
                            border: 2px dashed #cbd5e1;
                            margin-top: 8px;

                            qrcode {
                                display: block;
                            }
                        }
                    }

                    .qr-display {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: 15px;
                        margin-top: -8px;

                        .qr-instruction {
                            text-align: center;
                            font-size: 13px;
                            color: #475569;
                            font-weight: 500;
                            line-height: 1.4;
                            margin: 0 8px 2px 8px;
                        }

                        .qr-container {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            padding: 12px;
                            background: #f8fafc;
                            border-radius: 8px;
                            border: 2px dashed #cbd5e1;

                            qrcode {
                                display: block;
                            }
                        }
                    }

                    // Responsive adjustments
                    @media (max-width: 768px) {
                        gap: 4px;

                        .single-app-info {
                            gap: 10px;
                            padding: 6px 0 0 0;

                            .qr-instruction {
                                font-size: 12px;
                                margin: 0 8px;
                            }

                            .qr-container {
                                padding: 10px;
                                margin-top: 6px;
                            }
                        }

                        .qr-display {
                            gap: 6px;

                            .qr-instruction {
                                font-size: 12px;
                                margin: 0 8px 2px 8px;
                            }

                            .qr-container {
                                padding: 10px;
                            }
                        }

                        .app-selector {
                            margin-bottom: 2px;
                        }
                    }

                    @media (min-width: 1440px) {
                        gap: 8px;

                        .single-app-info {
                            gap: 14px;
                            padding: 10px 0 0 0;

                            .qr-instruction {
                                font-size: 14px;
                                margin: 0 8px;
                            }

                            .qr-container {
                                padding: 14px;
                                margin-top: 10px;
                            }
                        }

                        .qr-display {
                            gap: 8px;

                            .qr-instruction {
                                font-size: 14px;
                                margin: 0 8px 4px 8px;
                            }

                            .qr-container {
                                padding: 14px;
                            }
                        }

                        .app-selector {
                            margin-bottom: 6px;
                        }
                    }
                }

                .no-apps {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    text-align: center;
                    padding: 20px;

                    p {
                        font-size: 14px;
                        color: #64748b;
                        margin: 0;
                        line-height: 1.4;
                    }
                }
            }
        }
    }
}



.avatar-wrapper {
    position: relative;
    width: 64px;
    height: 64px;
    margin: 0;
}

.avatar {
    width: 64px !important;
    height: 64px !important;
    margin: 0 !important;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-initials {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color:#667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26px;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }
}