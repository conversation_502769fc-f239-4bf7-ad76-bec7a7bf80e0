<div id="forms" class="page-layout simple fullwidth" fxLayout="column">
    <!-- <div class="header p-20 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>Dashboard</h2>
            </div>
        </div>
    </div> -->
    <div style="display: flex; align-items: center; margin: 30px; justify-content: start; gap: 10px;">
        <div class="avatar-wrapper">
            <img class="avatar" [src]="picUrl" (error)="onImageError()" (load)="onImageLoad()"
                [style.display]="showImage ? 'block' : 'none'">
            <div class="avatar-initials" [style.display]="showImage ? 'none' : 'flex'" [title]="userName">
                {{getInitials()}}
            </div>
        </div>
        <div style="font-size: 32px; font-weight: 900; color: #002D68;">Welcome back, {{userName}}!</div>
    </div>
    <div class="content">
        <div class="cards-container">
            <fuse-widget *ngFor="let module of moduleList" class="flip-card">
                <div class="fuse-widget-front">
                    <div class="card-content">
                        <div class="image-section">
                            <img src='{{ "assets/images/logos/" + module.moduleDesc + ".png" }}'
                                 class="module-image"
                                 (click)="navigateToHousekeeping(module.URL)"
                                 alt="{{module.moduleDesc}}" />
                        </div>
                        <div class="content-section">
                            <h3 class="module-title">{{module.moduleDesc}}</h3>
                            <p class="module-description">{{module.Description}}</p>
                            <button class="read-more-btn" fuseWidgetToggle (click)="onCardFlip(module)">
                                Read More
                            </button>
                        </div>
                    </div>
                </div>
                <div class="fuse-widget-back">
                    <div class="back-content">
                        <button class="back-btn" fuseWidgetToggle>
                            <mat-icon>arrow_back</mat-icon>
                        </button>

                        <div class="back-main-content">
                            <h3 class="back-title">{{ module.moduleDesc }}</h3>

                            <div *ngIf="module.Applications?.length; else noApps" class="app-content">
                                <!-- Multiple apps dropdown -->
                                <div *ngIf="module.Applications.length > 1; else singleApp" class="app-selector">
                                    <mat-form-field appearance="fill" style="width: 100%;">
                                        <mat-label>Select an Application</mat-label>
                                        <mat-select [value]="getSelectedApp(module)" (selectionChange)="onAppSelectionChange(module, $event.value)">
                                            <mat-option *ngFor="let app of module.Applications" [value]="app">
                                                {{ app.AppName }}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                </div>

                                <!-- Single app fallback -->
                                <ng-template #singleApp>
                                    <div class="single-app-info">
                                        <div class="qr-instruction">
                                            <strong>Scan QR code to install application</strong>
                                        </div>
                                        <div class="qr-container">
                                            <qrcode
                                                [qrdata]="module.Applications[0].AppLink"
                                                [width]="100"
                                                [errorCorrectionLevel]="'M'">
                                            </qrcode>
                                        </div>
                                    </div>
                                </ng-template>

                                <!-- QR Code Display for selected app -->
                                <div *ngIf="getSelectedLink(module) && module.Applications.length > 1" class="qr-display">
                                    <div class="qr-instruction">
                                        <strong>Scan QR code to install application</strong>
                                    </div>
                                    <div class="qr-container">
                                        <qrcode [qrdata]="getSelectedLink(module)" [width]="100" [errorCorrectionLevel]="'M'"></qrcode>
                                    </div>
                                </div>
                            </div>

                            <ng-template #noApps>
                                <div class="no-apps">
                                    <p>No applications available for this module.</p>
                                </div>
                            </ng-template>
                        </div>
                    </div>
                </div>
            </fuse-widget>
        </div>
    </div>
</div>