import { Component, <PERSON>Child, On<PERSON><PERSON>roy } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { GridSearchRequestDto } from 'app/dto/common/gridDto';
import { CompanyList, CompanyResponseDto } from 'app/dto/companyDto';
import { SaveCompanyRequestDto } from 'app/dto/saveCompanyDto';
import { BaseComponent } from 'app/main/common/base.component';
import { CompanyApiService } from 'app/services/api/company.api.service';
import { CountryApiService } from 'app/services/api/country.api.service';
import { environment } from 'environments/environment';
import { Observable, Subject } from 'rxjs';
import { map, startWith, takeUntil } from 'rxjs/operators';
import { GlobalSearchService, SearchableComponent, SearchConfiguration } from 'app/services/global-search.service';
import * as moment from 'moment';
import { AdminList, FacilityGridDataResponseDto, ManageFacilityGridData, SaveFacilityRequestDto, SaveManageFacilityRequestDto, mstFacilityModule } from 'app/dto/facilityDto';
import { FacilityApiService } from 'app/services/api/facility.api.service.service';
import { FileuploaderComponent } from 'app/main/common/fileuploader/fileuploader.component';
import { ViewqrcodeComponent } from 'app/main/common/viewqrcode/viewqrcode.component';
//import { FacilityAdminPipe } from './facility-admin-pipe';
import { ModuleList } from 'app/dto/countryDto';
import { userIDDto } from 'app/dto/userDto';
import { ModuleApiService } from 'app/services/api/modules.api.service';

@Component({
  selector: 'app-manage-facility',
  templateUrl: './managefacility.component.html',
  styleUrls: ['./managefacility.component.scss']
})

export class ManagefacilityComponent extends BaseComponent implements SearchableComponent, OnDestroy {
  @ViewChild('formUser', { static: true }) ngForm;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  search_user = '';
  search_userEmail = '';
  search_userId = '';
  tkn = localStorage.getItem('SSOTOKEN');
  fgFacility = new FormGroup({
    facilityId: new FormControl('', Validators.required),
    facilityName: new FormControl(''),
    //facilityAdmin:  new FormControl(''),
    companyControl: new FormControl(''),
    logo: new FormControl(''),
    active: new FormControl(true),
    moduleDesc: new FormControl(''),
  });
  selectedRowIndex = 0;
  moduleList: ModuleList[];
  companyFilteredOptions: Observable<CompanyList[]> | undefined;
  isExistingUpdateMode = false;
  displayedColumns: string[] = ['facilityName', 'facilityCode', 'QR', 'company', 'facilityLogo', 'isActive', 'edit'];
  dataSource: MatTableDataSource<ManageFacilityGridData>;
  facilityGridData: FacilityGridDataResponseDto[] = [];
  companyList: CompanyList[];
  adminList: AdminList[];
  checkedOptions: AdminList[];
  length = 50;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageEvent: PageEvent;
  isFacilityEdit: boolean = false;

  // Search functionality
  private _unsubscribeAll: Subject<any> = new Subject<any>();
  private originalFacilityData: FacilityGridDataResponseDto[] = [];
  private searchConfig: SearchConfiguration = {
    searchableFields: ['facilityName', 'facilityCode', 'company'],
    caseSensitive: false,
    exactMatch: false,
    minSearchLength: 1
  };

  constructor(private _countryApi: CountryApiService, private _companyApi: CompanyApiService, private _facilityApi: FacilityApiService, private _moduleApi: ModuleApiService,
    _snackBar: MatSnackBar, public _dialog: MatDialog, private _router: Router, private _globalSearchService: GlobalSearchService) {
    super(_snackBar);
  }

  ngOnInit() {
    const isAd = localStorage.getItem('SSOADMIN');
    if (isAd == '1' && this.tkn.trim().length > 0) {
    } else {
      localStorage.setItem('SSOUSERNAME', '');
      localStorage.setItem('SSOUSERPIC', '');
      this._router.navigateByUrl('/login');
    }

    // this.paginator.pageSize = 5;
    // this.paginator.pageIndex = 1;
    this.getCompaniesList();
    this.getAdminList();
    this.getFacilityData();
    this.getAllModules();

    // Subscribe to global search
    this._globalSearchService.searchContext$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(context => {
        if (context.currentRoute.includes('/app/facility')) {
          this.onGlobalSearch(context.searchTerm);
        }
      });
  }

  getCompaniesList(): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: 0,
      pageSize: this.pageSize,
      searchString: this.search_user,
      UserEmail: this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._companyApi.getCompanies(obj)
      .subscribe(
        (data: any) => {
          this.companyList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c: CompanyList = {
              companyId: _obj.companyId,
              companyName: _obj.companyName
            };
            this.companyList.push(_c);
          }
          this.companyFilteredOptions = this.fgFacility.controls.companyControl.valueChanges.pipe(
            startWith(''),
            map(value => this._filter(value))
          );
        },
        _error => {
        }
      );
  }
  getAllModules(): void {
    this.search_userId = localStorage.getItem('SSOUSERCOMPANYID');
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: userIDDto = {
      id: this.search_userId,
      UserEmail: this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._moduleApi.getModules(obj)
      .subscribe(
        (data: any) => {
          this.moduleList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c: ModuleList = {
              moduleId: _obj.moduleId,
              moduleDesc: _obj.moduleDesc,
              URL: _obj.URL,
              Description: ''
            };
            this.moduleList.push(_c);
          }
        },
        _error => {
        }
      );
  }
  private _filter(value: string): CompanyList[] {
    const filterValue = value && value.length > 0 ? value.toLowerCase() : "";
    return this.companyList.filter(company =>
      company.companyName.toLowerCase().startsWith(filterValue));
  }

  saveFacilities(): void {
    //console.log(this.fgFacility);
    const facilityObj: SaveManageFacilityRequestDto = {
      isActive: (this.fgFacility.controls.active.value == '' || this.fgFacility.controls.active.value == null) ? false : this.fgFacility.controls.active.value,
      facilityName: this.fgFacility.controls.facilityName.value,
      facilityId: (this.fgFacility.controls.facilityId.value == '' || this.fgFacility.controls.facilityId.value == null) ? 0 : this.fgFacility.controls.facilityId.value,
      //facilityAdminList: this.checkedOptions.map(user => `${user.adminId} - ${user.adminName}`).toString(),
      facilityLogo: this.fgFacility.controls.logo.value,
      company: '',
      companyId: this.fgFacility.controls.companyControl.value,
      FacilityModules: [],
      createdby: Number(localStorage.getItem('SSOUSERID'))
    };
    // module mapp
    this.fgFacility.controls.moduleDesc.value.forEach(module => {
      const usrModules: mstFacilityModule = {
        refId: 0,
        appId: 1,
        moduleId: module,
        facilityId: 0
      };
      facilityObj.FacilityModules.push(usrModules);
    });
    const obj = this.wrapRequestObject(facilityObj);
    this._facilityApi.saveFacilityList(obj)
      .subscribe(
        (data) => {
          if (data.code === 1) {
            this.showSuccessMsg('Changes has been saved successfully');
            this.ngForm.resetForm();
            this.selectedRowIndex = 0;
            this.getCompaniesList();
            this.getFacilityData();
          } else {
            this.showErrorMsg(data.msg);
          }
        },
        _error => {
        }
      );

  }

  getAdminList(): void {
    this._facilityApi.getAdminLists()
      .subscribe(
        (data: any) => {
          this.adminList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c: AdminList = {
              adminId: _obj.userId,
              adminName: _obj.userFullName
            };
            this.adminList.push(_c);
          }
        },
        _error => {
        }
      );
  }

  getFacilityData(): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: 0,
      pageSize: this.pageSize,
      searchString: this.search_user,
      UserEmail: this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._facilityApi.getFacilitiesList(obj)
      .subscribe(
        (data) => {
          this.facilityGridData = [];
          const responseData = JSON.parse(data.obj.toString());
          this.length = responseData != null ? responseData.totalRecords : 0;
          if (responseData) {
            this.facilityGridData = responseData.gridData;
            this.originalFacilityData = [...responseData.gridData]; // Store original data for search
          }
          else {
            this.facilityGridData = [];
            this.originalFacilityData = [];
          }
          this.dataSource = new MatTableDataSource(this.facilityGridData);
          this.dataSource.sort = this.sort;
          this.paginator.firstPage();
        },
        _error => {
        }
      );
  }

  pagerEvent(event?: PageEvent): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: event.pageIndex,
      pageSize: event.pageSize,
      searchString: this.search_user,
      UserEmail: this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._facilityApi.getFacilitiesList(obj)
      .subscribe(
        (data) => {
          this.facilityGridData = [];
          const responseData = JSON.parse(data.obj.toString());
          if (responseData) {
            this.facilityGridData = responseData.gridData;
          }
          else {
            this.facilityGridData = [];
          }
          this.dataSource = new MatTableDataSource(this.facilityGridData);
          this.dataSource.sort = this.sort;
        },
        _error => {
        }
      );
  }

  resetExtraDetails(): void {
    this.search_user = '';
    this.selectedRowIndex = 0;
    this.isExistingUpdateMode = false;
    this.isFacilityEdit = false;
    this.getCompaniesList();
  }

  loadFacilityDetails(row) {
    this.isFacilityEdit = true;
    this.checkedOptions = [];
    this.isExistingUpdateMode = true;
    this.selectedRowIndex = row.facilityId;
    //const adminIdArray = row.facilityAdminList.split(",");
    this.fgFacility.controls.facilityId.setValue(row.facilityId);
    this.fgFacility.controls.facilityName.setValue(row.facilityName);
    this.fgFacility.controls.companyControl.setValue(row.companyId);
    //this.checkedOptions = this.adminList.filter(array => adminIdArray.find(filter => array.adminId === parseInt(filter, 10)));
    this.fgFacility.controls.logo.setValue(row.facilityLogo);
    let FacilityModulesList = [];
    row.FacilityModules.forEach(_userModules => {
      if (_userModules.AppId === 1) {
        FacilityModulesList.push(_userModules.ModuleId);
      }
    });
    this.fgFacility.controls.moduleDesc.setValue(FacilityModulesList);
  }

  change(event: PageEvent) {
    this.getFacilityData();
  }
  uploadLogo(): boolean {
    const dialogRef = this._dialog.open(FileuploaderComponent, {
      width: '400px',
      data: {
        name: 'Change Facility Logo',
        filePath: '',
        base64: this.fgFacility.controls['logo'].value
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log(result.data);
      if (result.data && result.data.length > 0) {
        this.fgFacility.controls.logo.setValue(result.data);
      }
      else {
        this.fgFacility.controls.logo.setValue(null);
      }
    });
    return false;
  }
  viewQRcode(row): boolean {
    const dialogRef = this._dialog.open(ViewqrcodeComponent, {
      width: '400px',
      data: {
        name: 'View QR Code ' + row.facilityCode,
        value: row.facilityUniqueId
      }
    });
    return false;
  }
  navigateToHousekeeping(): void {
    window.location.href = environment.hpbsUrl + '#/login?token=' + this.tkn;
  }

  /**
   * Implementation of SearchableComponent interface
   * @param searchTerm The search term from global search
   */
  onGlobalSearch(searchTerm: string): void {
    if (!searchTerm || searchTerm.trim() === '') {
      // Reset to original data when search is cleared
      this.facilityGridData = [...this.originalFacilityData];
    } else {
      // Filter data using the global search service
      this.facilityGridData = this._globalSearchService.filterData(
        this.originalFacilityData,
        searchTerm,
        this.searchConfig
      );
    }

    // Update the data source
    this.dataSource = new MatTableDataSource(this.facilityGridData);
    this.dataSource.sort = this.sort;
    this.paginator.firstPage();
  }

  /**
   * Component cleanup
   */
  ngOnDestroy(): void {
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

}
